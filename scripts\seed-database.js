import dotenv from "dotenv";
dotenv.config();

import mysql from 'mysql2/promise';
import bcrypt from 'bcryptjs';

const db = mysql.createPool({
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "3306"),
  user: process.env.DB_USER || "root1",
  password: process.env.DB_PASSWORD || "2002",
  database: process.env.DB_NAME || "employee_leave_management_system",
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
});

async function seedDatabase() {
  try {
    console.log('Starting database seeding...');

    // Check if admin user already exists
    const [existingAdmin] = await db.execute('SELECT * FROM users WHERE username = ? OR email = ?', ['admin', '<EMAIL>']);

    if (existingAdmin.length === 0) {
      // Create admin user
      const adminPassword = await bcrypt.hash('admin123', 10);
      await db.execute(`
        INSERT INTO users (username, email, password, role)
        VALUES (?, ?, ?, ?)
      `, ['admin', '<EMAIL>', adminPassword, 'admin']);
      console.log('Created admin user');
    } else {
      console.log('Admin user already exists');
    }

    // Create sample employees
    const employeePassword = await bcrypt.hash('employee123', 10);
    
    const employees = [
      ['john_doe', '<EMAIL>', employeePassword, 'employee'],
      ['jane_smith', '<EMAIL>', employeePassword, 'employee'],
      ['mike_johnson', '<EMAIL>', employeePassword, 'employee'],
      ['sarah_wilson', '<EMAIL>', employeePassword, 'employee'],
      ['david_brown', '<EMAIL>', employeePassword, 'employee']
    ];

    for (const employee of employees) {
      const [existingEmployee] = await db.execute('SELECT * FROM users WHERE username = ? OR email = ?', [employee[0], employee[1]]);

      if (existingEmployee.length === 0) {
        await db.execute(`
          INSERT INTO users (username, email, password, role)
          VALUES (?, ?, ?, ?)
        `, employee);
        console.log(`Created employee: ${employee[0]}`);
      } else {
        console.log(`Employee already exists: ${employee[0]}`);
      }
    }

    // Get user IDs for creating sample leaves
    const [users] = await db.execute('SELECT id, username FROM users WHERE role = "employee"');

    // Create sample leave requests
    const leaveTypes = ['Annual', 'Sick', 'Personal', 'Emergency'];
    const statuses = ['Pending', 'Approved', 'Rejected'];
    
    const sampleLeaves = [
      [users[0].id, 'John Doe', 'Annual', '2024-07-15', '2024-07-19', 'Family vacation to the beach', 'Approved'],
      [users[1].id, 'Jane Smith', 'Sick', '2024-06-20', '2024-06-22', 'Flu symptoms and recovery', 'Approved'],
      [users[2].id, 'Mike Johnson', 'Personal', '2024-08-01', '2024-08-03', 'Moving to new apartment', 'Pending'],
      [users[3].id, 'Sarah Wilson', 'Emergency', '2024-06-10', '2024-06-11', 'Family emergency - urgent care needed', 'Rejected'],
      [users[4].id, 'David Brown', 'Annual', '2024-09-15', '2024-09-25', 'Extended vacation to Europe', 'Pending'],
      [users[0].id, 'John Doe', 'Sick', '2024-05-15', '2024-05-16', 'Doctor appointment and recovery', 'Approved'],
      [users[1].id, 'Jane Smith', 'Personal', '2024-07-01', '2024-07-05', 'Wedding anniversary celebration', 'Pending'],
      [users[2].id, 'Mike Johnson', 'Annual', '2024-08-20', '2024-08-30', 'Summer vacation with family', 'Approved']
    ];

    for (const leave of sampleLeaves) {
      await db.execute(`
        INSERT INTO leaves (user_id, employee_name, leave_type, from_date, to_date, reason, status) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, leave);
    }

    console.log('Database seeding completed successfully!');
    console.log('\nSample Users Created:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('Employees:');
    console.log('  <EMAIL> / employee123');
    console.log('  <EMAIL> / employee123');
    console.log('  <EMAIL> / employee123');
    console.log('  <EMAIL> / employee123');
    console.log('  <EMAIL> / employee123');
    
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    await db.end();
  }
}

seedDatabase();

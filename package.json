{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=development tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "NODE_ENV=production node dist/index.js", "check": "tsc", "db:seed": "tsx scripts/seed-database.js", "db:view": "node view-database.js"}, "dependencies": {"@date-io/date-fns": "^3.2.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.2", "@mui/material": "^7.1.2", "@mui/x-date-pickers": "^8.5.3", "@tanstack/react-query": "^5.60.5", "bcryptjs": "^3.0.2", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.39.1", "express": "^4.21.2", "express-validator": "^7.2.1", "formik": "^2.4.6", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "react": "^18.3.1", "react-dom": "^18.3.1", "wouter": "^3.3.5", "yup": "^1.6.1"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.2.7", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@types/bcryptjs": "^2.4.6", "@types/dotenv": "^6.1.1", "@types/express": "4.17.21", "@types/jsonwebtoken": "^9.0.10", "@types/node": "20.16.11", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.2", "cross-env": "^7.0.3", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.19"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}